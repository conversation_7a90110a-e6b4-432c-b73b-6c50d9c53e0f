"use client"

import React, { useEffect } from "react"
import Image from "next/image"
import { X } from "lucide-react"
import {
  <PERSON>alog,
  DialogContent,
  DialogOverlay,
} from "@/components/ui/dialog"
import { cn } from "@/lib/utils"

interface ImageModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  src: string
  alt: string
  title?: string
  className?: string
}

export function ImageModal({
  open,
  onOpenChange,
  src,
  alt,
  title,
  className
}: ImageModalProps) {
  // Handle ESC key press
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && open) {
        onOpenChange(false)
      }
    }

    if (open) {
      document.addEventListener("keydown", handleKeyDown)
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden"
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown)
      document.body.style.overflow = "unset"
    }
  }, [open, onOpenChange])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className={cn(
          "max-w-[95vw] max-h-[95vh] w-auto h-auto p-0 border-0 bg-transparent shadow-none",
          "data-[state=open]:animate-in data-[state=closed]:animate-out",
          "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          "data-[state=closed]:duration-300 data-[state=open]:duration-300",
          className
        )}
        showCloseButton={false}
        onClick={(e) => {
          // Close modal when clicking on the backdrop
          if (e.target === e.currentTarget) {
            onOpenChange(false)
          }
        }}
      >
        <div className="relative bg-background rounded-lg overflow-hidden shadow-2xl">
          {/* Close button */}
          <button
            onClick={() => onOpenChange(false)}
            className="absolute top-4 right-4 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors duration-200"
            aria-label="Close image"
          >
            <X className="h-4 w-4" />
          </button>

          {/* Title */}
          {title && (
            <div className="absolute top-4 left-4 z-10 px-3 py-1 bg-black/50 text-white text-sm rounded-md">
              {title}
            </div>
          )}

          {/* Image container */}
          <div className="relative max-w-[90vw] max-h-[90vh] min-w-[300px] min-h-[200px]">
            <Image
              src={src}
              alt={alt}
              width={1200}
              height={800}
              className="w-full h-full object-contain"
              quality={95}
              priority
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/images/project-placeholder.svg";
              }}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Hook for managing image modal state
export function useImageModal() {
  const [isOpen, setIsOpen] = React.useState(false)
  const [imageData, setImageData] = React.useState<{
    src: string
    alt: string
    title?: string
  } | null>(null)

  const openModal = (src: string, alt: string, title?: string) => {
    setImageData({ src, alt, title })
    setIsOpen(true)
  }

  const closeModal = () => {
    setIsOpen(false)
    // Clear image data after animation completes
    setTimeout(() => setImageData(null), 300)
  }

  return {
    isOpen,
    imageData,
    openModal,
    closeModal,
    setIsOpen
  }
}
