"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MediaSelector } from '@/components/media-selector/MediaSelector';
import { DEFAULT_CONFIG, mergeConfig } from '@/components/media-selector';
import type { MediaSelectorConfig } from '@/components/media-selector';

export default function TestCropPage() {
  const [showMediaSelector, setShowMediaSelector] = useState(false);
  const [selectedImage, setSelectedImage] = useState<any>(null);

  const config: MediaSelectorConfig = mergeConfig(DEFAULT_CONFIG, {
    folder: 'test-crop',
    acceptedTypes: ['image/*'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    layout: 'dialog',
    enableUpload: true,
    cropConfig: {
      targetWidth: 1200,
      targetHeight: 800,
      quality: 90,
    },
    onSelect: (files) => {
      if (files.length > 0) {
        setSelectedImage(files[0]);
      }
      setShowMediaSelector(false);
    },
  });

  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Crop Editor Test</CardTitle>
          <p className="text-muted-foreground">
            Test the crop area movement functionality. The crop area should be able to move to all edges of the image.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={() => setShowMediaSelector(true)}>
            Select & Crop Image
          </Button>

          {selectedImage && (
            <div className="space-y-2">
              <h3 className="font-semibold">Selected Image:</h3>
              <img 
                src={selectedImage.url} 
                alt={selectedImage.originalName}
                className="max-w-full h-auto rounded-lg border"
              />
              <p className="text-sm text-muted-foreground">
                {selectedImage.originalName} - {selectedImage.size} bytes
              </p>
            </div>
          )}

          <div className="text-sm text-muted-foreground space-y-2">
            <h4 className="font-semibold">Test Instructions:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>Click "Select & Crop Image" to open the media selector</li>
              <li>Upload an image or select an existing one</li>
              <li>In the crop editor, try moving the crop area to all edges:</li>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Move to the top edge ✓ (should work)</li>
                <li>Move to the bottom edge ⚠️ (was problematic)</li>
                <li>Move to left and right edges</li>
              </ul>
              <li>Verify the crop area can reach the very bottom of the image</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {showMediaSelector && (
        <MediaSelector
          config={config}
          open={showMediaSelector}
          onClose={() => setShowMediaSelector(false)}
        />
      )}
    </div>
  );
}
