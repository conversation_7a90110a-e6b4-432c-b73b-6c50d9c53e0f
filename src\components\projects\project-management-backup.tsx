"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { Plus, Search, Edit, Trash2, Eye } from "lucide-react"
import { useLoading } from "@/contexts/loading-context"

// Helper function to get project image URL with fallback and cache busting
const getProjectImageUrl = (imageUrl?: string | null, bustCache: boolean = false): string => {
  const DEFAULT_PROJECT_IMAGE = "/images/project-placeholder.svg";
  if (!imageUrl) return DEFAULT_PROJECT_IMAGE;

  // Add cache busting for fresh images
  let finalUrl = imageUrl;
  if (bustCache) {
    const separator = imageUrl.includes('?') ? '&' : '?';
    finalUrl = `${imageUrl}${separator}v=${Date.now()}`;
  }

  return finalUrl;
}
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
// Dropdown menu imports removed - using visible action buttons instead
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ProjectDialog } from "./project-dialog"
import { DeleteProjectDialog } from "./delete-project-dialog"
import { ImageModal, useImageModal } from "@/components/ui/image-modal"

interface Project {
  id: string
  ad: string
  slug?: string
  aciklama?: string
  adres?: string
  baslangic_tarihi: string
  bitis_tarihi?: string
  project_image_url?: string
  _count: {
    bloklar: number
    arizalar: number
  }
  olusturulma_tarihi: string
}

interface ApiResponse {
  projects: Project[]
  pagination: {
    currentPage: number
    totalPages: number
    totalCount: number
    limit: number
  }
}

export function ProjectManagementBackup() {
  const router = useRouter()
  const { startLoading } = useLoading()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [showProjectDialog, setShowProjectDialog] = useState(false)
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingProject, setDeletingProject] = useState<Project | null>(null)

  // Image modal state
  const { isOpen: isImageModalOpen, imageData, openModal: openImageModal, closeModal: closeImageModal } = useImageModal()

  const limit = 10
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        mode: "management",
        page: currentPage.toString(),
        limit: limit.toString(),
        ...(searchQuery && { search: searchQuery }),
        // Cache busting için timestamp ekle
        t: Date.now().toString(),
      })

      const response = await fetch(`/api/projects?${params}`, {
        // Cache'i bypass et
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
        }
      })
      if (!response.ok) {
        throw new Error("Failed to fetch projects")
      }

      const data: ApiResponse = await response.json()
      setProjects(data.projects)
      setTotalPages(data.pagination.totalPages)
      setTotalCount(data.pagination.totalCount)
    } catch (error) {
      console.error("Error fetching projects:", error)
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchQuery, limit])
  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])
  const handleSearch = (value: string) => {
    setSearchQuery(value)
    setCurrentPage(1)
  }
  const handleProjectClick = (project: Project) => {
    startLoading()
    if (project.slug) {
      router.push(`/projeler/${project.slug}`)
    }
  }

  const handleImageClick = (project: Project, e: React.MouseEvent) => {
    e.stopPropagation() // Prevent row click
    const imageUrl = getProjectImageUrl(project.project_image_url, true)
    // Don't open modal for placeholder images
    if (imageUrl !== "/images/project-placeholder.svg") {
      openImageModal(imageUrl, project.ad, `${project.ad} - Proje Görseli`)
    }
  }

  // Artık modal açmak yerine sayfaya yönlendiriyoruz
  // Silme işlemi için modal kullanılmaya devam edebilir
  const handleDelete = (project: Project) => {
    setDeletingProject(project)
    setShowDeleteDialog(true)
  }
  const handleDeleteDialogClose = () => {
    setShowDeleteDialog(false)
    setDeletingProject(null)
    fetchProjects()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR")
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className="w-80 h-10 bg-gray-200 rounded-md animate-pulse"></div>
            </div>
          </div>
          <div className="w-32 h-10 bg-gray-200 rounded-md animate-pulse"></div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        </div>

        {/* Table Skeleton */}
        <Card>
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
                  <div className="flex-1 space-y-2">
                    <div className="w-48 h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-32 h-3 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Proje ara..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-8 w-80"
            />
          </div>
        </div>
        <Button asChild>
          <Link href="/projeler/yeni">
            <Plus className="h-4 w-4 mr-2" />
            Yeni Proje
          </Link>
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Proje</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Blok</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {projects.reduce((sum, p) => sum + p._count.bloklar, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Table - Original table implementation */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Resim</TableHead>
              <TableHead>Proje Adı</TableHead>
              <TableHead>Başlangıç</TableHead>
              <TableHead>Bitiş</TableHead>
              <TableHead>Blok Sayısı</TableHead>
              <TableHead>Arıza Sayısı</TableHead>
              <TableHead className="w-[120px]">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {projects.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  {searchQuery ? "Arama kriterlerine uygun proje bulunamadı" : "Henüz proje eklenmemiş"}
                </TableCell>
              </TableRow>
            ) : (projects.map((project) => (
                <TableRow
                  key={project.id}
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={(e) => {
                    const target = e.target as HTMLElement;
                    if (target.closest('button') || target.closest('a')) {
                      return;
                    }
                    handleProjectClick(project);
                  }}
                >
                  <TableCell>
                    <div
                      className="w-12 h-12 rounded-lg overflow-hidden bg-muted relative cursor-pointer hover:ring-2 hover:ring-blue-500 hover:ring-offset-2 transition-all duration-200 group"
                      onClick={(e) => handleImageClick(project, e)}
                      title={project.project_image_url ? "Görseli büyüt" : "Proje görseli yok"}
                    >
                      <Image
                        src={getProjectImageUrl(project.project_image_url, true)}
                        alt={project.ad}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-200"
                        sizes="48px"
                        quality={80}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/images/project-placeholder.svg";
                        }}
                        placeholder="empty"
                      />
                      {project.project_image_url && (
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                          <Eye className="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-blue-600">
                        {project.ad}
                      </div>
                      {project.aciklama && (
                        <div className="text-sm text-muted-foreground mt-1">
                          {project.aciklama}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(project.baslangic_tarihi)}</TableCell>
                  <TableCell>
                    {project.bitis_tarihi ? formatDate(project.bitis_tarihi) : "-"}
                  </TableCell>
                  <TableCell>{project._count.bloklar}</TableCell>
                  <TableCell>{project._count.arizalar}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleProjectClick(project);
                        }}
                        className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600"
                        title="Projeyi Görüntüle"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="h-8 w-8 p-0 hover:bg-orange-100 hover:text-orange-600"
                        title="Projeyi Düzenle"
                      >
                        <Link
                          href={`/projeler/${project.slug}/duzenle`}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(project);
                        }}
                        className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600"
                        title="Projeyi Sil"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      <DeleteProjectDialog
        open={showDeleteDialog}
        onClose={handleDeleteDialogClose}
        project={deletingProject}
      />

      {imageData && (
        <ImageModal
          open={isImageModalOpen}
          onOpenChange={closeImageModal}
          src={imageData.src}
          alt={imageData.alt}
          title={imageData.title}
        />
      )}
    </div>
  )
}
