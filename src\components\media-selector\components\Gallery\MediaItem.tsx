"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Check,
  MoreVertical,
  Edit,
  Trash2,
  Download,
  Eye,
  Copy,
  Tag,
  Crop,
} from 'lucide-react';

import type { MediaFile } from '../../types';
import { formatFileSize } from '../../utils/fileValidation';
import { gridItemVariants } from '../../utils/animations';
import { cn } from '@/lib/utils';

interface MediaItemProps {
  item: MediaFile;
  selected: boolean;
  onSelect: () => void;
  onToggleSelection: () => void;
  onEdit: () => void;
  onCrop: () => void;
  onDelete: () => void;
  view: 'grid' | 'list';
}

export function MediaItem({
  item,
  selected,
  onSelect,
  onToggleSelection,
  onEdit,
  onCrop,
  onDelete,
  view,
}: MediaItemProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const isImage = item.mimeType.startsWith('image/');
  const isVideo = item.mimeType.startsWith('video/');

  // Handle download
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = item.url;
    link.download = item.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle copy URL
  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(item.url);
      // You might want to show a toast notification here
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  // Grid view
  if (view === 'grid') {
    return (
      <motion.div
        variants={gridItemVariants}
        whileHover="hover"
        whileTap="tap"
        className={cn(
          "relative group rounded-lg overflow-hidden border-2 transition-all duration-200 cursor-pointer",
          "bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800",
          "hover:shadow-lg hover:shadow-blue-500/20",
          selected
            ? "border-blue-500 ring-2 ring-blue-500/20"
            : "border-gray-200 dark:border-gray-700 hover:border-blue-300"
        )}
        onClick={onSelect}
      >
        {/* Selection checkbox */}
        <div className="absolute top-2 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <Checkbox
            checked={selected}
            onCheckedChange={onToggleSelection}
            onClick={(e) => e.stopPropagation()}
            className="bg-white/90 backdrop-blur-sm"
          />
        </div>

        {/* Actions menu */}
        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 bg-white/90 backdrop-blur-sm hover:bg-white"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              {isImage && (
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  onCrop();
                }}>
                  <Crop className="h-4 w-4 mr-2" />
                  Crop
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                handleDownload();
              }}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                handleCopyUrl();
              }}>
                <Copy className="h-4 w-4 mr-2" />
                Copy URL
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Selected indicator */}
        {selected && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute top-2 right-2 z-20 bg-blue-500 text-white rounded-full p-1"
          >
            <Check className="h-3 w-3" />
          </motion.div>
        )}

        {/* Media preview */}
        <div className="aspect-square relative overflow-hidden bg-gray-100 dark:bg-gray-800">
          {isImage ? (
            <>
              {!imageLoaded && !imageError && (
                <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 animate-pulse" />
              )}
              <img
                src={item.thumbnailMedium || item.url}
                alt={item.alt || item.originalName}
                className={cn(
                  "w-full h-full object-cover transition-all duration-300",
                  imageLoaded ? "opacity-100" : "opacity-0",
                  "group-hover:scale-105"
                )}
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
              />
              {imageError && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
                  <Eye className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </>
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
              <div className="text-center">
                <div className="text-2xl mb-2">
                  {isVideo ? '🎥' : '📄'}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                  {item.mimeType.split('/')[1].toUpperCase()}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* File info overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3 text-white opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="text-sm font-medium truncate">
            {item.originalName}
          </div>
          <div className="text-xs text-gray-300">
            {formatFileSize(item.size)}
          </div>
          {item.tags && item.tags.length > 0 && (
            <div className="flex gap-1 mt-1">
              {item.tags.slice(0, 2).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {item.tags.length > 2 && (
                <Badge variant="secondary" className="text-xs">
                  +{item.tags.length - 2}
                </Badge>
              )}
            </div>
          )}
        </div>
      </motion.div>
    );
  }

  // List view
  return (
    <motion.div
      variants={gridItemVariants}
      whileHover="hover"
      className={cn(
        "flex items-center gap-4 p-4 rounded-lg border transition-all duration-200 cursor-pointer",
        "bg-gradient-to-r from-white to-gray-50 dark:from-gray-900 dark:to-gray-800",
        "hover:shadow-md hover:shadow-blue-500/10",
        selected
          ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20"
          : "border-gray-200 dark:border-gray-700 hover:border-blue-300"
      )}
      onClick={onSelect}
    >
      {/* Selection checkbox */}
      <Checkbox
        checked={selected}
        onCheckedChange={onToggleSelection}
        onClick={(e) => e.stopPropagation()}
      />

      {/* Thumbnail */}
      <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-100 dark:bg-gray-800 flex-shrink-0">
        {isImage ? (
          <img
            src={item.thumbnailSmall || item.url}
            alt={item.alt || item.originalName}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-lg">
            {isVideo ? '🎥' : '📄'}
          </div>
        )}
      </div>

      {/* File info */}
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{item.originalName}</div>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {formatFileSize(item.size)} • {new Date(item.createdAt).toLocaleDateString()}
        </div>
        {item.alt && (
          <div className="text-xs text-gray-400 truncate mt-1">
            {item.alt}
          </div>
        )}
      </div>

      {/* Tags */}
      {item.tags && item.tags.length > 0 && (
        <div className="flex gap-1 flex-wrap max-w-xs">
          {item.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
          {item.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{item.tags.length - 3}
            </Badge>
          )}
        </div>
      )}

      {/* Actions */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </DropdownMenuItem>
          {isImage && (
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              onCrop();
            }}>
              <Crop className="h-4 w-4 mr-2" />
              Crop
            </DropdownMenuItem>
          )}
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            handleDownload();
          }}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </DropdownMenuItem>
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            handleCopyUrl();
          }}>
            <Copy className="h-4 w-4 mr-2" />
            Copy URL
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="text-red-600"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </motion.div>
  );
}
