import type {
  MediaFile,
  MediaListResponse,
  MediaUploadResponse,
  MediaDeleteResponse,
  MediaUpdateResponse,
  MediaListRequest,
  MediaUploadRequest,
  MediaUpdateRequest,
  MediaDeleteRequest,
  UploadProgress,
} from '../types';

// API client class
export class MediaApiClient {
  private baseUrl: string;
  
  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }
  
  // List media files
  async listMedia(params: MediaListRequest): Promise<MediaListResponse> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    
    const url = `${this.baseUrl}/api/media?${searchParams.toString()}`;
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error listing media:', error);
      throw error;
    }
  }
  
  // Upload media file with progress tracking
  async uploadMedia(
    request: MediaUploadRequest,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<MediaUploadResponse> {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('folder', request.folder);
    
    if (request.alt) formData.append('alt', request.alt);
    if (request.caption) formData.append('caption', request.caption);
    if (request.tags) formData.append('tags', JSON.stringify(request.tags));
    if (request.cropConfig) formData.append('cropConfig', JSON.stringify(request.cropConfig));
    
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      const fileId = `upload-${Date.now()}-${Math.random().toString(36).substring(2)}`;
      
      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentage = (event.loaded / event.total) * 100;
            const speed = event.loaded / ((Date.now() - startTime) / 1000);
            const timeRemaining = (event.total - event.loaded) / speed;
            
            onProgress({
              fileId,
              filename: request.file.name,
              loaded: event.loaded,
              total: event.total,
              percentage,
              speed,
              timeRemaining,
              status: 'uploading',
            });
          }
        });
      }
      
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (onProgress) {
              onProgress({
                fileId,
                filename: request.file.name,
                loaded: request.file.size,
                total: request.file.size,
                percentage: 100,
                speed: 0,
                timeRemaining: 0,
                status: 'completed',
              });
            }
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid JSON response'));
          }
        } else {
          const error = `HTTP ${xhr.status}: ${xhr.statusText}`;
          if (onProgress) {
            onProgress({
              fileId,
              filename: request.file.name,
              loaded: 0,
              total: request.file.size,
              percentage: 0,
              speed: 0,
              timeRemaining: 0,
              status: 'error',
              error,
            });
          }
          reject(new Error(error));
        }
      });
      
      xhr.addEventListener('error', () => {
        const error = 'Network error during upload';
        if (onProgress) {
          onProgress({
            fileId,
            filename: request.file.name,
            loaded: 0,
            total: request.file.size,
            percentage: 0,
            speed: 0,
            timeRemaining: 0,
            status: 'error',
            error,
          });
        }
        reject(new Error(error));
      });
      
      const startTime = Date.now();
      xhr.open('POST', `${this.baseUrl}/api/upload`);
      xhr.send(formData);
    });
  }
  
  // Update media metadata
  async updateMedia(request: MediaUpdateRequest): Promise<MediaUpdateResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/media/${request.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          alt: request.alt,
          caption: request.caption,
          tags: request.tags,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating media:', error);
      throw error;
    }
  }
  
  // Delete media file
  async deleteMedia(request: MediaDeleteRequest): Promise<MediaDeleteResponse> {
    try {
      // The request.id could be either just the filename or the full URL
      // We need to construct the full URL path for the delete endpoint
      let fileUrl = request.id;

      // If the id is just a filename (doesn't start with /), construct the full URL
      if (!fileUrl.startsWith('/')) {
        fileUrl = `/${request.folder || 'media'}/${fileUrl}`;
      }

      const deleteUrl = `${this.baseUrl}/api/media?url=${encodeURIComponent(fileUrl)}`;

      console.log('🗑️ MediaApiClient: Deleting media file:', fileUrl);

      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ MediaApiClient: Delete failed:', response.status, errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ MediaApiClient: Delete successful:', result);
      return result;
    } catch (error) {
      console.error('❌ MediaApiClient: Error deleting media:', error);
      throw error;
    }
  }
  
  // Batch delete media files
  async batchDeleteMedia(ids: string[], folder?: string): Promise<{ results: Array<{ id: string; success: boolean; error?: string }> }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/media/batch-delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids,
          folder,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error batch deleting media:', error);
      throw error;
    }
  }
}

// Default API client instance
export const mediaApi = new MediaApiClient();

// Utility functions for API responses
export function isSuccessResponse<T>(response: { success: boolean; data?: T; error?: string }): response is { success: true; data: T } {
  return response.success && response.data !== undefined;
}

export function getErrorMessage(response: { success: boolean; error?: string; message?: string }): string {
  return response.error || response.message || 'Unknown error occurred';
}

// Retry utility for failed requests
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)));
    }
  }
  
  throw lastError!;
}

// Upload queue for managing multiple uploads
export class UploadQueue {
  private queue: Array<{
    id: string;
    request: MediaUploadRequest;
    onProgress?: (progress: UploadProgress) => void;
    resolve: (value: MediaUploadResponse) => void;
    reject: (error: Error) => void;
  }> = [];
  
  private activeUploads = new Set<string>();
  private maxConcurrent: number;
  
  constructor(maxConcurrent: number = 3) {
    this.maxConcurrent = maxConcurrent;
  }
  
  async add(
    request: MediaUploadRequest,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<MediaUploadResponse> {
    return new Promise((resolve, reject) => {
      const id = `upload-${Date.now()}-${Math.random().toString(36).substring(2)}`;
      
      this.queue.push({
        id,
        request,
        onProgress,
        resolve,
        reject,
      });
      
      this.processQueue();
    });
  }
  
  private async processQueue(): Promise<void> {
    if (this.activeUploads.size >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    const item = this.queue.shift();
    if (!item) return;
    
    this.activeUploads.add(item.id);
    
    try {
      const result = await mediaApi.uploadMedia(item.request, item.onProgress);
      item.resolve(result);
    } catch (error) {
      item.reject(error instanceof Error ? error : new Error(String(error)));
    } finally {
      this.activeUploads.delete(item.id);
      this.processQueue(); // Process next item in queue
    }
  }
  
  getQueueLength(): number {
    return this.queue.length;
  }
  
  getActiveUploads(): number {
    return this.activeUploads.size;
  }
  
  clear(): void {
    this.queue.forEach(item => {
      item.reject(new Error('Upload queue cleared'));
    });
    this.queue = [];
  }
}
