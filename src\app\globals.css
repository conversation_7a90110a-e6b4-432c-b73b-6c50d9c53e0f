@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the fault tracking system */
@layer components {
  /* Sidebar collapse styles */
  .sidebar-collapsed {
    width: 70px;
    overflow: hidden;
  }
  
  .sidebar-collapsed .sidebar-item-text {
    display: none;
  }
  
  .sidebar-collapsed .sidebar-item-icon {
    margin-right: 0;
  }
  
  .sidebar-collapsed .logo-text {
    display: none;
  }
  
  .sidebar-collapsed .logo-icon {
    margin-right: 0;
  }
  
  .sidebar-collapsed .user-info {
    display: none;
  }
  
  .sidebar-collapsed .expand-icon {
    transform: rotate(180deg);
  }

  /* Fault priority styles */
  .fault-critical {
    background-color: #fee2e2;
    border-left: 4px solid #ef4444;
  }
  
  .fault-high {
    background-color: #ffedd5;
    border-left: 4px solid #f97316;
  }
  
  .fault-medium {
    background-color: #fef9c3;
    border-left: 4px solid #eab308;
  }
  
  .fault-low {
    background-color: #ecfdf5;
    border-left: 4px solid #10b981;
  }

  /* Status badge styles */
  .status-new {
    background-color: #e0f2fe;
    color: #0369a1;
  }
  
  .status-assigned {
    background-color: #cffafe;
    color: #0e7490;
  }
  
  .status-in-progress {
    background-color: #ede9fe;
    color: #7e22ce;
  }
  
  .status-pending {
    background-color: #fef3c7;
    color: #b45309;
  }
  
  .status-completed {
    background-color: #dcfce7;
    color: #166534;
  }

  /* React Big Calendar custom styles */
  .rbc-calendar {
    font-family: inherit;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background: hsl(var(--background));
  }

  .rbc-header {
    background: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    font-weight: 600;
    padding: 0.5rem;
    border-bottom: 1px solid hsl(var(--border));
  }

  .rbc-month-view {
    border: none;
  }

  .rbc-date-cell {
    text-align: center;
    padding: 0.25rem;
    border-right: 1px solid hsl(var(--border));
  }

  .rbc-date-cell.rbc-off-range {
    color: hsl(var(--muted-foreground));
  }

  .rbc-today {
    background-color: hsl(var(--primary) / 0.1);
  }

  .rbc-event {
    border-radius: 4px;
    font-size: 0.75rem;
    padding: 2px 5px;
    margin: 1px;
    border: none;
  }

  .rbc-event:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  .rbc-slot-selection {
    background-color: hsl(var(--primary) / 0.2);
  }

  .rbc-toolbar {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .rbc-toolbar button {
    padding: 0.5rem 1rem;
    border: 1px solid hsl(var(--border));
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    border-radius: var(--radius);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
  }

  .rbc-toolbar button:hover {
    background: hsl(var(--muted));
  }

  .rbc-toolbar button.rbc-active {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border-color: hsl(var(--primary));
  }

  .rbc-toolbar-label {
    font-size: 1.25rem;
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .rbc-day-slot .rbc-time-slot {
    border-top: 1px solid hsl(var(--border));
  }

  .rbc-time-view {
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
  }

  .rbc-time-header {
    border-bottom: 1px solid hsl(var(--border));
  }

  .rbc-time-content {
    border-top: none;
  }

  .rbc-allday-cell {
    background: hsl(var(--muted));
  }

  .rbc-row-segment {
    padding: 0 1px;
  }
  
  .status-cancelled {
    background-color: #f3f4f6;
    color: #6b7280;
  }

  /* Dropdown hover effect */
  .dropdown:hover .dropdown-menu {
    display: block;
  }

  /* ReactCrop custom styles for proper boundary handling */
  .ReactCrop {
    position: relative;
    display: inline-block;
    cursor: crosshair;
    max-width: 100%;
    max-height: 100%;
  }

  .ReactCrop__crop-selection {
    position: absolute;
    top: 0;
    left: 0;
    transform: translate3d(0, 0, 0);
    box-sizing: border-box;
    border: 1px solid rgba(255, 255, 255, 0.5);
    background-color: rgba(0, 0, 0, 0.2);
    cursor: move;
  }

  .ReactCrop__crop-selection:focus {
    outline: none;
  }

  /* Ensure crop area can reach all edges */
  .ReactCrop__crop-selection--new {
    border: 1px dashed rgba(255, 255, 255, 0.7);
  }

  /* Mobile responsive styles */
  @media (max-width: 768px) {
    .sidebar {
      position: fixed;
      z-index: 50;
      transform: translateX(-100%);
      transition: transform 0.3s ease-in-out;
    }

    .sidebar-mobile-show {
      transform: translateX(0);
    }

    .sidebar-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0,0,0,0.5);
      z-index: 40;
    }

    .sidebar-overlay-show {
      display: block;
    }
  }
}
