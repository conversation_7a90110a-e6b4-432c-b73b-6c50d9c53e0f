"use client";

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import type {
  MediaSelectorState,
  MediaSelectorAction,
  MediaSelectorConfig,
  MediaFile,
  MediaFilters,
  CropData,
  UseMediaSelectorReturn,
} from './types';
import { DEFAULT_CONFIG, mergeConfig } from './types/config';
import { mediaApi, UploadQueue } from './utils/apiHelpers';
import { validateFiles } from './utils/fileValidation';

// Initial state
const initialState: MediaSelectorState = {
  items: [],
  selectedItems: new Set(),
  view: 'grid',
  currentTab: 'gallery',
  loading: false,
  uploading: false,
  uploadProgress: {},
  editingItem: null,
  editMode: null,
  cropData: null,
  searchQuery: '',
  filters: {
    type: 'all',
  },
  errors: {},
  config: DEFAULT_CONFIG,
};

// Reducer function
function mediaSelectorReducer(state: MediaSelectorState, action: MediaSelectorAction): MediaSelectorState {
  // Ensure editMode is always defined in the state
  const safeState = {
    ...state,
    editMode: state.editMode ?? null
  };

  switch (action.type) {
    case 'SET_CONFIG':
      return {
        ...safeState,
        config: mergeConfig(DEFAULT_CONFIG, action.payload),
        // Ensure editMode is preserved during config updates
        editMode: safeState.editMode,
      };

    case 'SET_LOADING':
      return {
        ...safeState,
        loading: action.payload,
      };

    case 'SET_UPLOADING':
      return {
        ...safeState,
        uploading: action.payload,
      };

    case 'SET_ITEMS':
      return {
        ...safeState,
        items: action.payload,
        loading: false,
      };

    case 'ADD_ITEM':
      return {
        ...state,
        items: [action.payload, ...state.items],
      };

    case 'UPDATE_ITEM':
      return {
        ...state,
        items: state.items.map(item =>
          item.id === action.payload.id
            ? { ...item, ...action.payload.updates }
            : item
        ),
      };

    case 'DELETE_ITEM':
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.payload),
        selectedItems: new Set([...state.selectedItems].filter(id => id !== action.payload)),
      };

    case 'SELECT_ITEM':
      return {
        ...state,
        selectedItems: new Set([action.payload]),
      };

    case 'DESELECT_ITEM':
      const newSelectedItems = new Set(state.selectedItems);
      newSelectedItems.delete(action.payload);
      return {
        ...state,
        selectedItems: newSelectedItems,
      };

    case 'TOGGLE_SELECTION':
      const toggledItems = new Set(state.selectedItems);
      if (toggledItems.has(action.payload)) {
        toggledItems.delete(action.payload);
      } else {
        toggledItems.add(action.payload);
      }
      return {
        ...state,
        selectedItems: toggledItems,
      };

    case 'SELECT_ALL':
      return {
        ...state,
        selectedItems: new Set(state.items.map(item => item.id)),
      };

    case 'CLEAR_SELECTION':
      return {
        ...state,
        selectedItems: new Set(),
      };

    case 'SET_VIEW':
      return {
        ...state,
        view: action.payload,
      };

    case 'SET_TAB':
      return {
        ...state,
        currentTab: action.payload,
      };

    case 'SET_SEARCH':
      return {
        ...state,
        searchQuery: action.payload,
      };

    case 'SET_FILTERS':
      return {
        ...state,
        filters: action.payload,
      };

    case 'SET_UPLOAD_PROGRESS':
      return {
        ...state,
        uploadProgress: {
          ...state.uploadProgress,
          [action.payload.fileId]: action.payload.progress,
        },
      };

    case 'START_EDIT':
      return {
        ...state,
        editingItem: action.payload,
        editMode: 'edit',
      };

    case 'SAVE_EDIT':
      return {
        ...state,
        items: state.items.map(item =>
          item.id === action.payload.id
            ? { ...item, ...action.payload.updates }
            : item
        ),
        editingItem: null,
        editMode: null,
      };

    case 'CANCEL_EDIT':
      return {
        ...state,
        editingItem: null,
        editMode: null,
        cropData: null,
      };

    case 'SET_CROP_DATA':
      return {
        ...state,
        cropData: action.payload,
      };

    case 'START_CROP':
      console.log('MediaSelector Reducer: START_CROP action triggered', {
        payload: action.payload,
        currentEditMode: safeState.editMode,
        newEditMode: 'crop'
      });
      const newState = {
        ...safeState,
        editingItem: action.payload,
        editMode: 'crop' as const,
        currentTab: 'gallery' as const,
      };
      console.log('MediaSelector Reducer: START_CROP new state created', {
        editMode: newState.editMode,
        editingItem: newState.editingItem?.originalName
      });
      return newState;

    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.payload.key]: action.payload.message,
        },
      };

    case 'CLEAR_ERROR':
      const { [action.payload]: _, ...remainingErrors } = state.errors;
      return {
        ...state,
        errors: remainingErrors,
      };

    case 'CLEAR_ALL_ERRORS':
      return {
        ...state,
        errors: {},
      };

    default:
      return state;
  }
}

// Context
const MediaSelectorContext = createContext<UseMediaSelectorReturn | null>(null);

// Upload queue instance
const uploadQueue = new UploadQueue(3);

// Provider component
interface MediaSelectorProviderProps {
  children: React.ReactNode;
  config: Partial<MediaSelectorConfig>;
}

export function MediaSelectorProvider({ children, config }: MediaSelectorProviderProps) {
  // Create initial state with guaranteed editMode and comprehensive defaults
  const providerInitialState: MediaSelectorState = {
    ...initialState,
    config: mergeConfig(DEFAULT_CONFIG, config),
    editMode: null, // Explicitly ensure editMode is defined
    // Ensure all required properties are defined
    items: [],
    selectedItems: new Set(),
    currentTab: 'gallery',
    loading: false,
    uploading: false,
    uploadProgress: {},
    editingItem: null,
    cropData: null,
    errors: {},
  };

  const [state, dispatch] = useReducer(mediaSelectorReducer, providerInitialState);

  // Config is set in initial state, no need to update it dynamically
  // as it can cause state loss for editMode and other properties

  // Load media files
  const loadMedia = useCallback(async () => {
    console.log('🔄 MediaSelector: Starting loadMedia for folder:', state.config.folder);
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ALL_ERRORS' });

    try {
      const response = await mediaApi.listMedia({
        folder: state.config.folder,
        search: state.searchQuery || undefined,
        type: state.filters.type !== 'all' ? state.filters.type : undefined,
      });

      console.log('📥 MediaSelector: API response received:', response);

      if (response.success && response.data) {
        console.log('✅ MediaSelector: Setting items, count:', response.data.length);
        dispatch({ type: 'SET_ITEMS', payload: response.data });
      } else {
        console.error('❌ MediaSelector: API response failed:', response.error);
        throw new Error(response.error || 'Failed to load media');
      }
    } catch (error) {
      console.error('❌ MediaSelector: loadMedia error:', error);
      const message = error instanceof Error ? error.message : 'Failed to load media';
      dispatch({ type: 'SET_ERROR', payload: { key: 'load', message } });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.config.folder, state.searchQuery, state.filters.type]);

  // Upload files
  const uploadFiles = useCallback(async (files: File[]) => {
    dispatch({ type: 'SET_UPLOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR', payload: 'upload' });

    // Validate files
    const validation = validateFiles(
      files,
      state.config.acceptedTypes,
      state.config.maxFileSize,
      state.config.maxFiles
    );

    if (!validation.valid) {
      const errorMessage = validation.errors.join(', ');
      dispatch({ type: 'SET_ERROR', payload: { key: 'upload', message: errorMessage } });
      dispatch({ type: 'SET_UPLOADING', payload: false });
      return;
    }

    try {
      const uploadPromises = files.map(file =>
        uploadQueue.add(
          {
            file,
            folder: state.config.folder,
          },
          (progress) => {
            dispatch({
              type: 'SET_UPLOAD_PROGRESS',
              payload: { fileId: progress.fileId, progress: progress.percentage },
            });
          }
        )
      );

      const results = await Promise.allSettled(uploadPromises);
      
      const uploadedFiles: MediaFile[] = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success && result.value.data) {
          dispatch({ type: 'ADD_ITEM', payload: result.value.data });
          uploadedFiles.push(result.value.data);
          state.config.onUpload?.(result.value.data);
        } else {
          const error = result.status === 'rejected' ? result.reason.message : 'Upload failed';
          dispatch({ type: 'SET_ERROR', payload: { key: `upload-${index}`, message: error } });
        }
      });

      // If crop config is enabled and we have uploaded files, automatically start crop for the first file
      if (state.config.cropConfig && uploadedFiles.length > 0) {
        const firstUploadedFile = uploadedFiles[0];
        console.log('MediaSelector: Auto-crop check - cropConfig enabled, uploaded files:', uploadedFiles.length);
        console.log('MediaSelector: First uploaded file:', firstUploadedFile);

        // Only auto-crop images
        if (firstUploadedFile.mimeType.startsWith('image/')) {
          console.log('MediaSelector: Starting auto-crop for image:', firstUploadedFile.originalName);
          dispatch({ type: 'START_CROP', payload: firstUploadedFile });
        } else {
          console.log('MediaSelector: Non-image file, switching to gallery tab');
          // Switch to gallery tab for non-images
          dispatch({ type: 'SET_TAB', payload: 'gallery' });
        }
      } else {
        console.log('MediaSelector: No crop config or no uploaded files, switching to gallery tab');
        // Switch to gallery tab after upload if no crop config
        dispatch({ type: 'SET_TAB', payload: 'gallery' });
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Upload failed';
      dispatch({ type: 'SET_ERROR', payload: { key: 'upload', message } });
    } finally {
      dispatch({ type: 'SET_UPLOADING', payload: false });
    }
  }, [state.config]);

  // Delete item
  const deleteItem = useCallback(async (urlOrId: string) => {
    try {
      // Find the item by URL or ID to get the correct ID for state management
      const item = state.items.find(item => item.url === urlOrId || item.id === urlOrId);
      if (!item) {
        throw new Error('Item not found');
      }

      const response = await mediaApi.deleteMedia({ id: urlOrId, folder: state.config.folder });

      if (response.success) {
        // Use the item's ID for state management (removing from the list)
        dispatch({ type: 'DELETE_ITEM', payload: item.id });
        state.config.onDelete?.(item.id);
      } else {
        throw new Error(response.error || 'Delete failed');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Delete failed';
      dispatch({ type: 'SET_ERROR', payload: { key: 'delete', message } });
    }
  }, [state.config, state.items]);

  // Actions object
  const actions = {
    loadMedia,
    uploadFiles,
    deleteItem,
    addItem: useCallback((item: MediaFile) => {
      dispatch({ type: 'ADD_ITEM', payload: item });
    }, []),
    selectItem: useCallback((id: string) => {
      dispatch({ type: 'SELECT_ITEM', payload: id });

      // Auto-crop functionality: If crop config is enabled and maxFiles is 1, automatically start crop
      if (state.config.cropConfig && state.config.maxFiles === 1) {
        const selectedItem = state.items.find(item => item.id === id);
        if (selectedItem && selectedItem.mimeType.startsWith('image/')) {
          console.log('MediaSelector: Auto-crop triggered for selected image:', selectedItem.originalName);
          dispatch({ type: 'START_CROP', payload: selectedItem });
        }
      }
    }, [state.config.cropConfig, state.config.maxFiles, state.items]),
    toggleSelection: useCallback((id: string) => {
      dispatch({ type: 'TOGGLE_SELECTION', payload: id });
    }, []),
    selectAll: useCallback(() => {
      dispatch({ type: 'SELECT_ALL' });
    }, []),
    clearSelection: useCallback(() => {
      dispatch({ type: 'CLEAR_SELECTION' });
    }, []),
    setSearch: useCallback((query: string) => {
      dispatch({ type: 'SET_SEARCH', payload: query });
    }, []),
    setFilters: useCallback((filters: MediaFilters) => {
      dispatch({ type: 'SET_FILTERS', payload: filters });
    }, []),
    startEdit: useCallback((item: MediaFile) => {
      dispatch({ type: 'START_EDIT', payload: item });
    }, []),
    startCrop: useCallback((item: MediaFile) => {
      dispatch({ type: 'START_CROP', payload: item });
    }, []),
    saveEdit: useCallback(async (id: string, updates: Partial<MediaFile>) => {
      try {
        const response = await mediaApi.updateMedia({ id, ...updates });
        
        if (response.success && response.data) {
          dispatch({ type: 'SAVE_EDIT', payload: { id, updates: response.data } });
        } else {
          throw new Error(response.error || 'Update failed');
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : 'Update failed';
        dispatch({ type: 'SET_ERROR', payload: { key: 'update', message } });
      }
    }, []),
    cancelEdit: useCallback(() => {
      dispatch({ type: 'CANCEL_EDIT' });
    }, []),
    setTab: useCallback((tab: 'gallery' | 'upload') => {
      dispatch({ type: 'SET_TAB', payload: tab });
    }, []),
    setView: useCallback((view: 'grid' | 'list') => {
      dispatch({ type: 'SET_VIEW', payload: view });
    }, []),
  };

  const value: UseMediaSelectorReturn = {
    state,
    actions,
  };

  return (
    <MediaSelectorContext.Provider value={value}>
      {children}
    </MediaSelectorContext.Provider>
  );
}

// Hook to use the media selector context
export function useMediaSelector(): UseMediaSelectorReturn {
  const context = useContext(MediaSelectorContext);
  
  if (!context) {
    throw new Error('useMediaSelector must be used within a MediaSelectorProvider');
  }
  
  return context;
}
