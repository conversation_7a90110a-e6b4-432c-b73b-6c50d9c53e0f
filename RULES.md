# 📋 Bakım Onarım Yönetim Sistemi - Kurallar ve Yönergeler

## 📖 Genel Bilgiler

**Sistem Adı:** Bakım Onarım Yönetim Sistemi  
**Versiyon:** 1.7.1  
**Açıklama:** Profesyonel bakım onarım yönetim platformu  
**Son Güncelleme:** 2025-01-13  

---

## 🔐 1. GÜVENLİK KURALLARI

### 1.1 Kimlik Doğrulama ve Yetkilendirme

#### Şifre Politikaları
- **Minimum uzunluk:** 8 karakter
- **Maksimum uzunluk:** 128 karakter
- **Zorunlu karakterler:**
  - En az 1 küçük harf (a-z)
  - En az 1 büyük harf (A-Z)
  - En az 1 rakam (0-9)
- **<PERSON><PERSON><PERSON> hashleme:** bcrypt (12 rounds)
- **<PERSON><PERSON><PERSON>:** Kullanıcılar düzenli olarak şifrelerini değiştirmelidir

#### Oturum Yönetimi
- **Oturum süresi:** JWT tabanlı oturum yönetimi
- **Otomatik çıkış:** Güvenlik nedeniyle belirli süre sonra otomatik çıkış
- **Çoklu oturum:** Aynı kullanıcı birden fazla cihazda oturum açabilir
- **Oturum güvenliği:** Secure, HttpOnly cookies kullanımı

#### Rol Tabanlı Erişim Kontrolü (RBAC)
- **ADMIN:** Tam sistem erişimi, tüm kullanıcı ve sistem yönetimi
- **MANAGER:** Proje yönetimi, teknisyen atama, raporlama
- **TECHNICIAN:** Arıza görüntüleme, randevu yönetimi, malzeme kullanımı
- **USER:** Arıza bildirimi, kendi arızalarını takip etme

### 1.2 API Güvenliği

#### Rate Limiting
- **Genel API:** 100 istek/dakika/IP
- **Authentication:** 5 başarısız giriş/dakika/IP
- **Upload:** 10 dosya/dakika/kullanıcı
- **Aşım durumu:** Geçici IP engelleme (15 dakika)

#### Güvenlik Headers
```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
X-XSS-Protection: 1; mode=block
```

#### Input Validation
- **Zod şemaları** ile tüm girdi validasyonu
- **SQL Injection** koruması (Prisma ORM)
- **XSS** koruması (input sanitization)
- **CSRF** koruması (NextAuth.js)

---

## 👥 2. KULLANICI YÖNETİMİ KURALLARI

### 2.1 Kullanıcı Kayıt ve Onay Süreci

#### Kayıt Kuralları
- **Email:** Benzersiz olmalı, geçerli format
- **Telefon:** 10-11 haneli Türkiye formatı (05XXXXXXXXX)
- **Ad/Soyad:** 2-50 karakter arası, özel karakter yasak
- **Proje/Blok/Daire:** Kayıt sırasında seçim zorunlu

#### Onay Süreci
- **Yeni kullanıcılar:** PENDING durumunda başlar
- **Admin onayı:** ADMIN rolündeki kullanıcılar onaylar
- **Aktif durum:** ACTIVE durumunda oturum açabilir
- **Reddedilme:** REJECTED durumunda sistem erişimi yok

### 2.2 Kullanıcı Durumları
- **PENDING:** Onay bekliyor
- **ACTIVE:** Aktif kullanıcı
- **INACTIVE:** Geçici olarak devre dışı
- **REJECTED:** Reddedilmiş
- **SUSPENDED:** Askıya alınmış

---

## 🏗️ 3. PROJE YÖNETİMİ KURALLARI

### 3.1 Proje Hiyerarşisi
```
Proje → Blok → Daire → Arıza
```

### 3.2 Proje Kuralları
- **Proje adı:** 1-100 karakter, benzersiz slug
- **Başlangıç tarihi:** Zorunlu
- **Bitiş tarihi:** Opsiyonel
- **Proje görseli:** Maksimum 2MB, JPEG/PNG/WebP

### 3.3 Blok ve Daire Kuralları
- **Blok adı:** 1-50 karakter
- **Daire numarası:** 1-20 karakter, blok içinde benzersiz
- **Kat bilgisi:** Opsiyonel, sayısal değer
- **Silme koruması:** Aktif arızası olan daire silinemez

---

## 🔧 4. ARIZA YÖNETİMİ KURALLARI

### 4.1 Arıza Oluşturma Kuralları

#### Zorunlu Alanlar
- **Başlık:** 5-100 karakter
- **Açıklama:** 10-1000 karakter
- **Kategori:** Önceden tanımlı kategorilerden seçim
- **Bildiren kişi:** Ad soyad ve telefon

#### Arıza Durumları
- **ACIK:** Yeni bildirilen arıza
- **BEKLEMEDE:** Teknisyen ataması bekleniyor
- **DEVAM_EDIYOR:** Teknisyen çalışıyor
- **COZULDU:** Arıza çözüldü
- **IPTAL:** Arıza iptal edildi

#### Aciliyet Seviyeleri
- **DUSUK:** Yeşil (Seviye 1)
- **ORTA:** Sarı (Seviye 2)
- **YUKSEK:** Turuncu (Seviye 3)
- **KRITIK:** Kırmızı (Seviye 4)

### 4.2 Arıza Takip Kuralları
- **Durum değişikliği:** Açıklama ile birlikte loglanır
- **Yorum sistemi:** 5-500 karakter arası
- **Fotoğraf ekleme:** Maksimum 5 dosya, 2MB/dosya
- **Otomatik bildirim:** Durum değişikliklerinde email/SMS

---

## 👨‍🔧 5. TEKNİSYEN YÖNETİMİ KURALLARI

### 5.1 Teknisyen Atama Kuralları
- **Uzmanlık alanı:** Arıza kategorisi ile eşleşmeli
- **Müsaitlik:** Randevu çakışması kontrolü
- **Coğrafi konum:** Proje yakınlığı öncelik
- **İş yükü:** Eşit dağılım prensibi

### 5.2 Randevu Yönetimi
- **Randevu saatleri:** 08:00-18:00 arası
- **Süre:** Minimum 30 dakika, maksimum 4 saat
- **İptal süresi:** En az 2 saat önceden
- **Tekrar planlama:** Maksimum 3 kez

### 5.3 Malzeme Kullanımı
- **Malzeme kaydı:** Kullanılan malzemeler loglanır
- **Stok kontrolü:** Otomatik stok düşürme
- **Onay süreci:** Pahalı malzemeler için yönetici onayı
- **Fatura entegrasyonu:** Malzeme maliyeti hesaplama

---

## 📁 6. DOSYA YÖNETİMİ KURALLARI

### 6.1 Dosya Yükleme Kuralları

#### Desteklenen Formatlar
- **Resim:** JPEG, PNG, WebP
- **Dokuman:** PDF
- **Maksimum boyut:** 2MB/dosya
- **Maksimum sayı:** 5 dosya/arıza

#### Güvenlik Önlemleri
- **Dosya tipi kontrolü:** MIME type validation
- **Virus tarama:** Yüklenen dosyalar taranır
- **Dosya adı:** Güvenli karakterlere dönüştürme
- **Otomatik optimizasyon:** WebP formatına dönüştürme

### 6.2 Medya Yönetimi
- **Klasör yapısı:** Proje/Blok/Daire hiyerarşisi
- **Thumbnail:** Otomatik küçük resim oluşturma
- **CDN entegrasyonu:** Hızlı dosya erişimi
- **Backup:** Günlük otomatik yedekleme

---

## 🚀 7. PERFORMANS VE CACHE KURALLARI

### 7.1 Cache Politikaları

#### Cache Süreleri (TTL)
- **İstatistikler:** 5 dakika
- **Kullanıcılar:** 10 dakika
- **Teknisyenler:** 15 dakika
- **Projeler:** 30 dakika
- **Kategoriler:** 1 saat
- **Statik içerik:** 1 saat

#### Cache Invalidation
- **Veri değişikliği:** İlgili cache'ler temizlenir
- **Bulk operations:** Tag-based invalidation
- **Redis cluster:** Distributed cache management

### 7.2 Database Optimizasyonu
- **Index kullanımı:** Sık sorgulanan alanlar
- **Query optimization:** N+1 problem önleme
- **Connection pooling:** Maksimum 20 bağlantı
- **Soft delete:** Veri bütünlüğü için

---

## 📊 8. RAPORLAMA VE ANALİTİK KURALLARI

### 8.1 Veri Gizliliği
- **Kişisel veri:** KVKK uyumlu işleme
- **Anonimleştirme:** Raporlarda kişisel bilgi gizleme
- **Erişim kontrolü:** Rol bazlı rapor erişimi
- **Audit log:** Tüm veri erişimleri loglanır

### 8.2 Rapor Türleri
- **Arıza raporları:** Durum, kategori, süre analizi
- **Teknisyen performansı:** Çözüm süresi, başarı oranı
- **Proje istatistikleri:** Genel sağlık durumu
- **Maliyet analizi:** Malzeme ve işçilik maliyetleri

---

## 🔄 9. BACKUP VE RECOVERY KURALLARI

### 9.1 Yedekleme Politikası
- **Günlük backup:** Otomatik PostgreSQL dump
- **Haftalık backup:** Tam sistem yedeği
- **Aylık backup:** Uzun dönem arşiv
- **Dosya backup:** Günlük medya dosyaları

### 9.2 Recovery Prosedürü
- **RTO (Recovery Time Objective):** 4 saat
- **RPO (Recovery Point Objective):** 1 saat
- **Test recovery:** Aylık recovery testi
- **Disaster recovery:** Alternatif lokasyon planı

---

## 📱 10. API KULLANIM KURALLARI

### 10.1 API Endpoints
- **Authentication:** `/api/auth/*`
- **Users:** `/api/users/*`
- **Projects:** `/api/projects/*`
- **Faults:** `/api/faults/*`
- **Technicians:** `/api/technicians/*`
- **Media:** `/api/media/*`

### 10.2 API Güvenlik
- **Authentication:** Bearer token gerekli
- **Rate limiting:** Endpoint bazlı limitler
- **CORS:** Sadece izinli domainler
- **Versioning:** API versiyon yönetimi

---

## ⚠️ 11. HATA YÖNETİMİ VE LOGGİNG

### 11.1 Error Handling
- **Global error boundary:** React error yakalama
- **API error responses:** Standart format
- **User-friendly messages:** Türkçe hata mesajları
- **Error recovery:** Otomatik yeniden deneme

### 11.2 Logging Politikası
- **Application logs:** Winston logger
- **Access logs:** Nginx access logs
- **Error logs:** Detaylı hata kayıtları
- **Audit logs:** Kritik işlem kayıtları
- **Log retention:** 90 gün saklama

---

## 🔧 12. MAINTENANCE VE GÜNCELLEMELER

### 12.1 Planlı Bakım
- **Bakım penceresi:** Pazar 02:00-06:00
- **Önceden bildirim:** 48 saat önceden
- **Rollback planı:** Hızlı geri alma prosedürü
- **Health check:** Bakım sonrası kontroller

### 12.2 Güvenlik Güncellemeleri
- **Kritik güncellemeler:** 24 saat içinde
- **Rutin güncellemeler:** Haftalık
- **Dependency updates:** Aylık kontrol
- **Security audit:** Üç aylık güvenlik denetimi

---

## 📞 13. DESTEK VE İLETİŞİM

### 13.1 Teknik Destek
- **Destek saatleri:** 7/24 kritik sorunlar
- **Response time:** 
  - Kritik: 1 saat
  - Yüksek: 4 saat
  - Orta: 24 saat
  - Düşük: 72 saat

### 13.2 İletişim Kanalları
- **Email:** <EMAIL>
- **Telefon:** +90 XXX XXX XX XX
- **Ticket sistemi:** Dahili destek sistemi
- **Dokümantasyon:** Wiki ve API docs

---

## 📋 14. UYUMLULUK VE STANDARTLAR

### 14.1 Yasal Uyumluluk
- **KVKK:** Kişisel Verilerin Korunması Kanunu
- **ISO 27001:** Bilgi güvenliği yönetimi
- **GDPR:** Avrupa veri koruma yönetmeliği
- **Türk Ceza Kanunu:** Bilişim suçları

### 14.2 Teknik Standartlar
- **REST API:** RESTful servis tasarımı
- **OAuth 2.0:** Güvenli yetkilendirme
- **JWT:** Token tabanlı kimlik doğrulama
- **HTTPS:** Tüm iletişimde SSL/TLS

---

## 📈 15. PERFORMANS METRIKLERI

### 15.1 Sistem Performansı
- **Response time:** < 200ms (API)
- **Page load time:** < 2 saniye
- **Uptime:** %99.9 availability
- **Concurrent users:** 1000+ eşzamanlı

### 15.2 İş Metrikleri
- **Arıza çözüm süresi:** Ortalama 24 saat
- **Teknisyen verimliliği:** %85+ kullanım
- **Kullanıcı memnuniyeti:** 4.5/5 puan
- **Sistem adoption:** %90+ kullanım oranı

---

## 🔄 16. SÜREKLI İYİLEŞTİRME

### 16.1 Feedback Döngüsü
- **Kullanıcı geri bildirimi:** Aylık anket
- **Performans analizi:** Haftalık review
- **Feature requests:** Quarterly planning
- **Bug reports:** Immediate action

### 16.2 Teknoloji Güncellemeleri
- **Framework updates:** Next.js, React
- **Database optimization:** PostgreSQL tuning
- **Infrastructure scaling:** Cloud resources
- **Security enhancements:** Ongoing improvements

---

**Son Güncelleme:** 2025-01-13  
**Doküman Sahibi:** Sistem Yöneticisi  
**Onay:** Proje Yöneticisi  
**Versiyon:** 1.0
