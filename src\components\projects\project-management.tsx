"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Filter,
  Grid3X3,
  List,
  Calendar,
  Building2,
  AlertTriangle,
  TrendingUp,
  BarChart3,
  Users,
  MapPin,
  Clock,
  Star,
  ArrowUpRight,
  Zap,
  Target,
  X
} from "lucide-react"
import { useLoading } from "@/contexts/loading-context"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts"

// Helper function to get project image URL with fallback and cache busting
const getProjectImageUrl = (imageUrl?: string | null, bustCache: boolean = false): string => {
  const DEFAULT_PROJECT_IMAGE = "/images/project-placeholder.svg";
  if (!imageUrl) return DEFAULT_PROJECT_IMAGE;

  // Add cache busting for fresh images
  let finalUrl = imageUrl;
  if (bustCache) {
    const separator = imageUrl.includes('?') ? '&' : '?';
    finalUrl = `${imageUrl}${separator}v=${Date.now()}`;
  }

  return finalUrl;
}
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { DeleteProjectDialog } from "./delete-project-dialog"
import { ImageModal, useImageModal } from "@/components/ui/image-modal"
import { cn } from "@/lib/utils"

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
}

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  },
  hover: {
    y: -8,
    scale: 1.02,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  }
}

const statsVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  }
}

interface Project {
  id: string
  ad: string
  slug?: string
  aciklama?: string
  adres?: string
  baslangic_tarihi: string
  bitis_tarihi?: string
  project_image_url?: string
  _count: {
    bloklar: number
    arizalar: number
  }
  olusturulma_tarihi: string
}

interface ApiResponse {
  projects: Project[]
  pagination: {
    currentPage: number
    totalPages: number
    totalCount: number
    limit: number
  }
}

type ViewMode = 'grid' | 'list'
type SortBy = 'name' | 'date' | 'blocks' | 'faults'
type FilterBy = 'all' | 'active' | 'completed'

export function ProjectManagement() {
  const router = useRouter()
  const { startLoading } = useLoading()

  // Core state
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  // Modern UI state
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortBy>('date')
  const [filterBy, setFilterBy] = useState<FilterBy>('all')
  const [selectedProjects, setSelectedProjects] = useState<Set<string>>(new Set())
  const [showFilters, setShowFilters] = useState(false)

  // Dialog states
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingProject, setDeletingProject] = useState<Project | null>(null)
  const [previewProject, setPreviewProject] = useState<Project | null>(null)

  // Image modal state
  const { isOpen: isImageModalOpen, imageData, openModal: openImageModal, closeModal: closeImageModal } = useImageModal()

  const limit = 12 // Increased for better grid layout

  // Computed statistics
  const stats = useMemo(() => {
    const totalBlocks = projects.reduce((sum, p) => sum + p._count.bloklar, 0)
    const totalFaults = projects.reduce((sum, p) => sum + p._count.arizalar, 0)
    const activeProjects = projects.filter(p => !p.bitis_tarihi).length
    const completedProjects = projects.filter(p => p.bitis_tarihi).length
    const avgBlocksPerProject = projects.length > 0 ? Math.round(totalBlocks / projects.length) : 0
    const avgFaultsPerProject = projects.length > 0 ? Math.round(totalFaults / projects.length) : 0

    return {
      totalProjects: totalCount,
      totalBlocks,
      totalFaults,
      activeProjects,
      completedProjects,
      avgBlocksPerProject,
      avgFaultsPerProject,
      completionRate: totalCount > 0 ? Math.round((completedProjects / totalCount) * 100) : 0
    }
  }, [projects, totalCount])

  // Chart data for project statistics
  const chartData = useMemo(() => {
    const monthlyData = projects.reduce((acc, project) => {
      const month = new Date(project.baslangic_tarihi).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'short'
      })
      acc[month] = (acc[month] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(monthlyData)
      .map(([month, count]) => ({ month, count }))
      .sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime())
      .slice(-6) // Last 6 months
  }, [projects])

  // Filtered and sorted projects
  const filteredProjects = useMemo(() => {
    let filtered = projects.filter(project => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        return (
          project.ad.toLowerCase().includes(query) ||
          project.aciklama?.toLowerCase().includes(query) ||
          project.adres?.toLowerCase().includes(query)
        )
      }
      return true
    })

    // Status filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(project => {
        if (filterBy === 'active') return !project.bitis_tarihi
        if (filterBy === 'completed') return !!project.bitis_tarihi
        return true
      })
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.ad.localeCompare(b.ad, 'tr')
        case 'date':
          return new Date(b.baslangic_tarihi).getTime() - new Date(a.baslangic_tarihi).getTime()
        case 'blocks':
          return b._count.bloklar - a._count.bloklar
        case 'faults':
          return b._count.arizalar - a._count.arizalar
        default:
          return 0
      }
    })

    return filtered
  }, [projects, searchQuery, filterBy, sortBy])
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        mode: "management",
        page: currentPage.toString(),
        limit: limit.toString(),
        ...(searchQuery && { search: searchQuery }),
        t: Date.now().toString(),
      })

      const response = await fetch(`/api/projects?${params}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
        }
      })
      if (!response.ok) {
        throw new Error("Failed to fetch projects")
      }

      const data: ApiResponse = await response.json()
      setProjects(data.projects)
      setTotalPages(data.pagination.totalPages)
      setTotalCount(data.pagination.totalCount)
    } catch (error) {
      console.error("Error fetching projects:", error)
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchQuery, limit])
  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  // Handler functions
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value)
    setCurrentPage(1)
  }, [])

  const handleProjectClick = useCallback((project: Project) => {
    startLoading()
    if (project.slug) {
      router.push(`/projeler/${project.slug}`)
    }
  }, [router, startLoading])

  const handleProjectSelect = useCallback((projectId: string) => {
    setSelectedProjects(prev => {
      const newSet = new Set(prev)
      if (newSet.has(projectId)) {
        newSet.delete(projectId)
      } else {
        newSet.add(projectId)
      }
      return newSet
    })
  }, [])

  const handleSelectAll = useCallback(() => {
    if (selectedProjects.size === filteredProjects.length) {
      setSelectedProjects(new Set())
    } else {
      setSelectedProjects(new Set(filteredProjects.map(p => p.id)))
    }
  }, [selectedProjects.size, filteredProjects])

  const handleQuickPreview = useCallback((project: Project) => {
    setPreviewProject(project)
  }, [])

  const handleImageClick = useCallback((project: Project, e: React.MouseEvent) => {
    e.stopPropagation()
    const imageUrl = getProjectImageUrl(project.project_image_url, true)
    if (imageUrl !== "/images/project-placeholder.svg") {
      openImageModal(imageUrl, project.ad, `${project.ad} - Proje Görseli`)
    }
  }, [openImageModal])

  const handleDelete = useCallback((project: Project) => {
    setDeletingProject(project)
    setShowDeleteDialog(true)
  }, [])

  const handleDeleteDialogClose = useCallback(() => {
    setShowDeleteDialog(false)
    setDeletingProject(null)
    fetchProjects()
  }, [fetchProjects])

  // Utility functions
  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR")
  }, [])

  const getProjectStatus = useCallback((project: Project) => {
    if (project.bitis_tarihi) {
      return { status: 'completed', label: 'Tamamlandı', color: 'bg-green-500' }
    }
    const startDate = new Date(project.baslangic_tarihi)
    const now = new Date()
    const daysDiff = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

    if (daysDiff < 30) {
      return { status: 'new', label: 'Yeni', color: 'bg-blue-500' }
    } else if (daysDiff < 90) {
      return { status: 'active', label: 'Aktif', color: 'bg-orange-500' }
    } else {
      return { status: 'ongoing', label: 'Devam Ediyor', color: 'bg-purple-500' }
    }
  }, [])

  const getProjectProgress = useCallback((project: Project) => {
    // Simple progress calculation based on faults vs blocks ratio
    const totalBlocks = project._count.bloklar
    const totalFaults = project._count.arizalar
    if (totalBlocks === 0) return 0

    // Assume fewer faults relative to blocks means better progress
    const faultRatio = totalFaults / totalBlocks
    const progress = Math.max(0, Math.min(100, 100 - (faultRatio * 20)))
    return Math.round(progress)
  }, [])

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="space-y-8"
      >
        {/* Modern Header Skeleton */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="space-y-2">
            <div className="h-8 w-64 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
            <div className="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="flex items-center gap-3">
            <div className="h-10 w-32 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="h-10 w-10 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="h-10 w-40 bg-gradient-to-r from-blue-200 to-purple-200 rounded-lg animate-pulse"></div>
          </div>
        </div>

        {/* Enhanced Stats Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 animate-pulse"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-gray-300 rounded animate-pulse mb-2"></div>
                <div className="h-3 w-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Chart Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg animate-pulse"></div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <div className="h-6 w-32 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-12 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 animate-pulse"></div>
              <div className="aspect-video bg-gray-200 animate-pulse"></div>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="h-5 w-3/4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                  <div className="flex justify-between items-center">
                    <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Modern Header with Enhanced Search and Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl text-white">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Proje Yönetimi
              </h1>
              <p className="text-sm text-muted-foreground">
                {stats.totalProjects} proje • {stats.activeProjects} aktif • {stats.completionRate}% tamamlanma
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Enhanced Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Proje, açıklama veya adres ara..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 w-80 bg-white/80 backdrop-blur-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-8 w-8 p-0"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8 w-8 p-0"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          {/* Filters */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <Filter className="h-4 w-4" />
                Filtrele
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => setFilterBy('all')}>
                Tüm Projeler
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterBy('active')}>
                Aktif Projeler
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterBy('completed')}>
                Tamamlanan Projeler
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* New Project Button */}
          <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg">
            <Link href="/projeler/yeni">
              <Plus className="h-4 w-4 mr-2" />
              Yeni Proje
            </Link>
          </Button>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div variants={statsVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-300">
            <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -translate-y-10 translate-x-10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-700">Toplam Proje</CardTitle>
              <div className="p-2 bg-blue-500 rounded-lg">
                <Building2 className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-900">{stats.totalProjects}</div>
              <div className="flex items-center gap-1 text-xs text-blue-600 mt-1">
                <TrendingUp className="h-3 w-3" />
                <span>{stats.activeProjects} aktif</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={statsVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-all duration-300">
            <div className="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -translate-y-10 translate-x-10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-700">Toplam Blok</CardTitle>
              <div className="p-2 bg-green-500 rounded-lg">
                <Grid3X3 className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-900">{stats.totalBlocks}</div>
              <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                <Target className="h-3 w-3" />
                <span>Ort. {stats.avgBlocksPerProject}/proje</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={statsVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-lg transition-all duration-300">
            <div className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -translate-y-10 translate-x-10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-700">Toplam Arıza</CardTitle>
              <div className="p-2 bg-orange-500 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-orange-900">{stats.totalFaults}</div>
              <div className="flex items-center gap-1 text-xs text-orange-600 mt-1">
                <Zap className="h-3 w-3" />
                <span>Ort. {stats.avgFaultsPerProject}/proje</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={statsVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-300">
            <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -translate-y-10 translate-x-10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-700">Tamamlanma</CardTitle>
              <div className="p-2 bg-purple-500 rounded-lg">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-900">{stats.completionRate}%</div>
              <div className="w-full bg-purple-200 rounded-full h-2 mt-2">
                <div
                  className="bg-purple-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${stats.completionRate}%` }}
                ></div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Interactive Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Project Timeline Chart */}
        <Card className="lg:col-span-2 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Proje Başlangıç Trendi
            </CardTitle>
            <p className="text-sm text-muted-foreground">Son 6 ay içinde başlayan projeler</p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={chartData}>
                  <defs>
                    <linearGradient id="colorProjects" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis
                    dataKey="month"
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                  />
                  <YAxis
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #E5E7EB',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'medium' }}
                  />
                  <Area
                    type="monotone"
                    dataKey="count"
                    stroke="#3B82F6"
                    strokeWidth={3}
                    fill="url(#colorProjects)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Hızlı İstatistikler
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium">Aktif Projeler</span>
              </div>
              <span className="text-lg font-bold text-blue-600">{stats.activeProjects}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium">Tamamlanan</span>
              </div>
              <span className="text-lg font-bold text-green-600">{stats.completedProjects}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm font-medium">Ortalama Blok</span>
              </div>
              <span className="text-lg font-bold text-orange-600">{stats.avgBlocksPerProject}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span className="text-sm font-medium">Ortalama Arıza</span>
              </div>
              <span className="text-lg font-bold text-purple-600">{stats.avgFaultsPerProject}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Projects Display */}
      {/* Modern Projects Display */}
      <AnimatePresence mode="wait">
        {filteredProjects.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center py-16"
          >
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Building2 className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {searchQuery ? "Arama sonucu bulunamadı" : "Henüz proje eklenmemiş"}
            </h3>
            <p className="text-gray-500 mb-6">
              {searchQuery
                ? "Arama kriterlerinizi değiştirip tekrar deneyin"
                : "İlk projenizi ekleyerek başlayın"
              }
            </p>
            {!searchQuery && (
              <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Link href="/projeler/yeni">
                  <Plus className="h-4 w-4 mr-2" />
                  İlk Projeyi Ekle
                </Link>
              </Button>
            )}
          </motion.div>
        ) : viewMode === 'grid' ? (
          <motion.div
            key="grid"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredProjects.map((project, index) => {
              const status = getProjectStatus(project)
              const progress = getProjectProgress(project)

              return (
                <motion.div
                  key={project.id}
                  variants={cardVariants}
                  initial="hidden"
                  animate="visible"
                  whileHover="hover"
                  transition={{ delay: index * 0.05 }}
                  className="group"
                >
                  <Card className="relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                    onClick={() => handleProjectClick(project)}
                  >
                    {/* Project Image */}
                    <div className="relative aspect-video overflow-hidden">
                      <Image
                        src={getProjectImageUrl(project.project_image_url, true)}
                        alt={project.ad}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                        quality={85}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/images/project-placeholder.svg";
                        }}
                      />

                      {/* Overlay with quick actions */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="absolute bottom-4 left-4 right-4 flex justify-between items-end">
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="secondary"
                              className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleImageClick(project, e)
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="secondary"
                              className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                              asChild
                            >
                              <Link
                                href={`/projeler/${project.slug}/duzenle`}
                                onClick={(e) => e.stopPropagation()}
                              >
                                <Edit className="h-4 w-4" />
                              </Link>
                            </Button>
                          </div>
                          <Button
                            size="sm"
                            variant="destructive"
                            className="h-8 w-8 p-0 bg-red-500/90 hover:bg-red-600"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDelete(project)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Status Badge */}
                      <div className="absolute top-4 left-4">
                        <Badge
                          className={cn(
                            "text-white border-0 shadow-lg",
                            status.color
                          )}
                        >
                          {status.label}
                        </Badge>
                      </div>

                      {/* Progress indicator */}
                      <div className="absolute top-4 right-4">
                        <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                          <div className="text-xs font-bold text-gray-700">{progress}%</div>
                        </div>
                      </div>
                    </div>

                    {/* Card Content */}
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Project Title and Description */}
                        <div>
                          <h3 className="font-bold text-lg text-gray-900 mb-1 line-clamp-1">
                            {project.ad}
                          </h3>
                          {project.aciklama && (
                            <p className="text-sm text-gray-600 line-clamp-2">
                              {project.aciklama}
                            </p>
                          )}
                        </div>

                        {/* Project Details */}
                        <div className="space-y-3">
                          {/* Location */}
                          {project.adres && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4 text-gray-400" />
                              <span className="line-clamp-1">{project.adres}</span>
                            </div>
                          )}

                          {/* Dates */}
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span>{formatDate(project.baslangic_tarihi)}</span>
                            {project.bitis_tarihi && (
                              <>
                                <span>-</span>
                                <span>{formatDate(project.bitis_tarihi)}</span>
                              </>
                            )}
                          </div>

                          {/* Progress Bar */}
                          <div className="space-y-1">
                            <div className="flex justify-between text-xs text-gray-600">
                              <span>İlerleme</span>
                              <span>{progress}%</span>
                            </div>
                            <Progress value={progress} className="h-2" />
                          </div>
                        </div>

                        {/* Statistics */}
                        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                          <div className="text-center">
                            <div className="flex items-center justify-center gap-1 text-blue-600 mb-1">
                              <Building2 className="h-4 w-4" />
                              <span className="text-lg font-bold">{project._count.bloklar}</span>
                            </div>
                            <p className="text-xs text-gray-500">Blok</p>
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center gap-1 text-orange-600 mb-1">
                              <AlertTriangle className="h-4 w-4" />
                              <span className="text-lg font-bold">{project._count.arizalar}</span>
                            </div>
                            <p className="text-xs text-gray-500">Arıza</p>
                          </div>
                        </div>

                        {/* Quick Action Button */}
                        <Button
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleProjectClick(project)
                          }}
                        >
                          <ArrowUpRight className="h-4 w-4 mr-2" />
                          Projeyi Görüntüle
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        ) : (
          /* List View */
          <motion.div
            key="list"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            {filteredProjects.map((project, index) => {
              const status = getProjectStatus(project)
              const progress = getProjectProgress(project)

              return (
                <motion.div
                  key={project.id}
                  variants={cardVariants}
                  initial="hidden"
                  animate="visible"
                  transition={{ delay: index * 0.03 }}
                >
                  <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                    onClick={() => handleProjectClick(project)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center gap-6">
                        {/* Project Image */}
                        <div className="relative w-20 h-20 rounded-xl overflow-hidden flex-shrink-0">
                          <Image
                            src={getProjectImageUrl(project.project_image_url, true)}
                            alt={project.ad}
                            fill
                            className="object-cover transition-transform duration-300 group-hover:scale-110"
                            sizes="80px"
                            quality={80}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "/images/project-placeholder.svg";
                            }}
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                            <Eye className="h-5 w-5 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </div>
                        </div>

                        {/* Project Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1 min-w-0">
                              <h3 className="font-bold text-lg text-gray-900 mb-1 truncate">
                                {project.ad}
                              </h3>
                              {project.aciklama && (
                                <p className="text-sm text-gray-600 line-clamp-2">
                                  {project.aciklama}
                                </p>
                              )}
                            </div>
                            <Badge
                              className={cn(
                                "text-white border-0 ml-4",
                                status.color
                              )}
                            >
                              {status.label}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                            {/* Dates */}
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <div>
                                <div>{formatDate(project.baslangic_tarihi)}</div>
                                {project.bitis_tarihi && (
                                  <div className="text-xs text-gray-500">
                                    {formatDate(project.bitis_tarihi)}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Location */}
                            {project.adres && (
                              <div className="flex items-center gap-2 text-sm text-gray-600">
                                <MapPin className="h-4 w-4 text-gray-400" />
                                <span className="truncate">{project.adres}</span>
                              </div>
                            )}

                            {/* Stats */}
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-1 text-blue-600">
                                <Building2 className="h-4 w-4" />
                                <span className="font-semibold">{project._count.bloklar}</span>
                                <span className="text-xs text-gray-500">blok</span>
                              </div>
                              <div className="flex items-center gap-1 text-orange-600">
                                <AlertTriangle className="h-4 w-4" />
                                <span className="font-semibold">{project._count.arizalar}</span>
                                <span className="text-xs text-gray-500">arıza</span>
                              </div>
                            </div>

                            {/* Progress */}
                            <div className="space-y-1">
                              <div className="flex justify-between text-xs text-gray-600">
                                <span>İlerleme</span>
                                <span>{progress}%</span>
                              </div>
                              <Progress value={progress} className="h-2" />
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleImageClick(project, e)
                            }}
                            className="h-9 w-9 p-0"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            asChild
                            className="h-9 w-9 p-0"
                          >
                            <Link
                              href={`/projeler/${project.slug}/duzenle`}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDelete(project)
                            }}
                            className="h-9 w-9 p-0 hover:bg-red-50 hover:text-red-600 hover:border-red-200"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Enhanced Pagination */}
      {totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-8"
        >
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">{filteredProjects.length}</span> proje gösteriliyor
            {searchQuery && (
              <span className="ml-2 text-blue-600">
                "{searchQuery}" için sonuçlar
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className="gap-2"
            >
              <ArrowUpRight className="h-4 w-4 rotate-180" />
              Önceki
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter(page =>
                  page === 1 ||
                  page === totalPages ||
                  (page >= currentPage - 1 && page <= currentPage + 1)
                )
                .map((page, index, array) => (
                  <div key={page} className="flex items-center">
                    {index > 0 && array[index - 1] !== page - 1 && (
                      <span className="px-2 text-muted-foreground">...</span>
                    )}
                    <Button
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className={cn(
                        "w-10 h-10",
                        currentPage === page && "bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                      )}
                    >
                      {page}
                    </Button>
                  </div>
                ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="gap-2"
            >
              Sonraki
              <ArrowUpRight className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>
      )}

      {/* Dialogs and Modals */}
      <DeleteProjectDialog
        open={showDeleteDialog}
        onClose={handleDeleteDialogClose}
        project={deletingProject}
      />

      {imageData && (
        <ImageModal
          open={isImageModalOpen}
          onOpenChange={closeImageModal}
          src={imageData.src}
          alt={imageData.alt}
          title={imageData.title}
        />
      )}

      {/* Quick Preview Modal */}
      <AnimatePresence>
        {previewProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setPreviewProject(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold">{previewProject.ad}</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setPreviewProject(null)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-3 text-sm">
                {previewProject.aciklama && (
                  <p className="text-gray-600">{previewProject.aciklama}</p>
                )}

                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <span className="text-gray-500">Blok Sayısı</span>
                    <div className="font-semibold">{previewProject._count.bloklar}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Arıza Sayısı</span>
                    <div className="font-semibold">{previewProject._count.arizalar}</div>
                  </div>
                </div>

                <Button
                  className="w-full mt-4"
                  onClick={() => {
                    setPreviewProject(null)
                    handleProjectClick(previewProject)
                  }}
                >
                  Detayları Görüntüle
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
